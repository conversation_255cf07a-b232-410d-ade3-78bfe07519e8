// Test script to verify autolayout improvements
const { calculateAutoLayout } = require("./lib/autoLayout.ts");

// Create a complex workflow that would cause overlaps
const complexNodes = [
  { id: "start", position: { x: 0, y: 0 }, data: { label: "Start" } },
  { id: "llm", position: { x: 0, y: 0 }, data: { label: "LLM" } },
  { id: "knowledge", position: { x: 0, y: 0 }, data: { label: "Knowledge" } },
  { id: "api", position: { x: 0, y: 0 }, data: { label: "API" } },
  { id: "memory1", position: { x: 0, y: 0 }, data: { label: "Memory 1" } },
  { id: "memory2", position: { x: 0, y: 0 }, data: { label: "Memory 2" } },
  { id: "condition", position: { x: 0, y: 0 }, data: { label: "Condition" } },
  { id: "function", position: { x: 0, y: 0 }, data: { label: "Function" } },
  { id: "response", position: { x: 0, y: 0 }, data: { label: "Response" } },
];

const complexEdges = [
  { id: "e1", source: "start", target: "llm" },
  { id: "e2", source: "start", target: "knowledge" },
  { id: "e3", source: "llm", target: "memory1" },
  { id: "e4", source: "knowledge", target: "api" },
  { id: "e5", source: "api", target: "memory2" },
  { id: "e6", source: "memory1", target: "condition" },
  { id: "e7", source: "memory2", target: "condition" },
  { id: "e8", source: "condition", target: "function" },
  { id: "e9", source: "function", target: "response" },
  { id: "e10", source: "memory2", target: "function" }, // Creates potential overlap
];

console.log("Testing autolayout with complex workflow...");

// Test with dynamic spacing system
const result = calculateAutoLayout(complexNodes, complexEdges, {
  horizontalSpacing: 350, // Base spacing - will be adjusted dynamically
  verticalSpacing: 250, // Base spacing - will be adjusted dynamically
  minimizeEdgeCrossings: true,
  edgeCrossingIterations: 5,
  dynamicSpacing: true, // Enable dynamic spacing
  edgeNodeSpacing: 80, // Base edge-node spacing
  edgeNodeCollisionIterations: 6,
});

console.log("\nLayout Results:");
console.log("Orientation:", result.orientation);
console.log("Node Positions:");

// Check for overlaps
const positions = Array.from(result.positions.entries());
let hasOverlaps = false;

// Check node-to-node overlaps
for (let i = 0; i < positions.length; i++) {
  for (let j = i + 1; j < positions.length; j++) {
    const [nodeId1, pos1] = positions[i];
    const [nodeId2, pos2] = positions[j];

    const distance = Math.sqrt(
      Math.pow(pos1.x - pos2.x, 2) + Math.pow(pos1.y - pos2.y, 2),
    );

    // Minimum required distance (node width + height + dynamic spacing)
    // For 8 nodes, the multiplier should be around 0.9, so ~315px base spacing
    const minDistance = 240 + 80 + 80; // Using the new dynamic spacing calculation method

    if (distance < minDistance) {
      console.log(
        `⚠️  OVERLAP DETECTED: ${nodeId1} and ${nodeId2} are too close (${distance.toFixed(2)} < ${minDistance.toFixed(2)})`,
      );
      hasOverlaps = true;
    }

    console.log(`  ${nodeId1}: (${pos1.x.toFixed(1)}, ${pos1.y.toFixed(1)})`);
  }
}

if (!hasOverlaps) {
  console.log("\n✅ SUCCESS: No node overlaps detected!");
} else {
  console.log("\n❌ FAILURE: Node overlaps still exist");
}

// Check spacing consistency
const spacings = [];
for (let i = 0; i < positions.length - 1; i++) {
  const [, pos1] = positions[i];
  const [, pos2] = positions[i + 1];
  const spacing = Math.abs(pos1.x - pos2.x) + Math.abs(pos1.y - pos2.y);
  spacings.push(spacing);
}

const avgSpacing = spacings.reduce((a, b) => a + b, 0) / spacings.length;
console.log(`\nAverage spacing: ${avgSpacing.toFixed(2)}`);
console.log(`Minimum spacing: ${Math.min(...spacings).toFixed(2)}`);
console.log(`Maximum spacing: ${Math.max(...spacings).toFixed(2)}`);

console.log("\n=== Test Complete ===");
