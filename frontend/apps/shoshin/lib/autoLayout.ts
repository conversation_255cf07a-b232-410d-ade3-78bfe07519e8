"use client";

import {
  getSmoothStepPath,
  Position,
  type Edge,
  type Node,
} from "@xyflow/react";

export interface LayoutOptions {
  horizontalSpacing?: number;
  verticalSpacing?: number;
  startX?: number;
  startY?: number;
  alignByLayer?: boolean;
  handleOrientation?: "horizontal" | "vertical" | "auto";
  animationDuration?: number;
  minimizeEdgeCrossings?: boolean;
  edgeCrossingIterations?: number;
  dynamicSpacing?: boolean;
  edgeNodeSpacing?: number;
  edgeNodeCollisionIterations?: number;
  onComplete?: (finalPositions: Map<string, { x: number; y: number }>) => void;
}

export interface AutoLayoutResult {
  positions: Map<string, { x: number; y: number }>;
  orientation: "horizontal" | "vertical";
}

/**
 * Detects the predominant handle orientation in the workflow
 */
export const detectHandleOrientation = (
  nodes: Node[],
): "horizontal" | "vertical" => {
  if (nodes.length === 0) return "horizontal";

  // For now, default to horizontal layout
  // This can be enhanced to detect based on node connections
  return "horizontal";
};

/**
 * Calculate the barycenter (average position) of connected nodes in adjacent levels
 */
const calculateBarycenter = (
  nodeId: string,
  level: number,
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  reverseAdjacencyList: Map<string, string[]>,
  orientation: "horizontal" | "vertical",
): number => {
  const currentLevelNodes = levelNodes.get(level) || [];
  const currentIndex = currentLevelNodes.indexOf(nodeId);

  // Get connected nodes from previous and next levels
  const connectedNodes: string[] = [];

  // Add nodes from previous level (sources)
  const prevLevelNodes = levelNodes.get(level - 1) || [];
  const sources = reverseAdjacencyList.get(nodeId) || [];
  sources.forEach((sourceId) => {
    if (prevLevelNodes.includes(sourceId)) {
      connectedNodes.push(sourceId);
    }
  });

  // Add nodes from next level (targets)
  const nextLevelNodes = levelNodes.get(level + 1) || [];
  const targets = adjacencyList.get(nodeId) || [];
  targets.forEach((targetId) => {
    if (nextLevelNodes.includes(targetId)) {
      connectedNodes.push(targetId);
    }
  });

  if (connectedNodes.length === 0) {
    return currentIndex; // Return current position if no connections
  }

  // Calculate average position of connected nodes
  let totalPosition = 0;
  connectedNodes.forEach((connectedId) => {
    // Find the position of connected node in its level
    for (const [connectedLevel, nodes] of levelNodes.entries()) {
      const connectedIndex = nodes.indexOf(connectedId);
      if (connectedIndex !== -1) {
        totalPosition += connectedIndex;
        break;
      }
    }
  });

  return totalPosition / connectedNodes.length;
};

/**
 * Minimize edge crossings using barycenter heuristic
 */
const minimizeEdgeCrossingsWithBarycenter = (
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  reverseAdjacencyList: Map<string, string[]>,
  orientation: "horizontal" | "vertical",
  iterations: number = 3,
): Map<number, string[]> => {
  const optimizedLevelNodes = new Map<number, string[]>();

  // Copy original level nodes
  levelNodes.forEach((nodes, level) => {
    optimizedLevelNodes.set(level, [...nodes]);
  });

  // Apply barycenter heuristic for specified iterations
  for (let iter = 0; iter < iterations; iter++) {
    // Process levels in alternating directions
    const levels = Array.from(optimizedLevelNodes.keys()).sort((a, b) => a - b);

    for (const level of levels) {
      const nodes = optimizedLevelNodes.get(level) || [];
      if (nodes.length <= 1) continue;

      // Calculate barycenter for each node
      const nodeBarycenter = nodes.map((nodeId) => ({
        nodeId,
        barycenter: calculateBarycenter(
          nodeId,
          level,
          optimizedLevelNodes,
          adjacencyList,
          reverseAdjacencyList,
          orientation,
        ),
      }));

      // Sort nodes by barycenter
      nodeBarycenter.sort((a, b) => a.barycenter - b.barycenter);

      // Update level with sorted nodes
      optimizedLevelNodes.set(
        level,
        nodeBarycenter.map((item) => item.nodeId),
      );
    }
  }

  return optimizedLevelNodes;
};

/**
 * Calculate dynamic spacing based on edge density between levels
 */
const calculateDynamicSpacing = (
  levelNodes: Map<number, string[]>,
  adjacencyList: Map<string, string[]>,
  baseSpacing: number,
  orientation: "horizontal" | "vertical",
): number => {
  let maxEdgeCount = 0;

  // Count edges between adjacent levels
  for (const [level, nodes] of levelNodes.entries()) {
    const nextLevelNodes = levelNodes.get(level + 1);
    if (!nextLevelNodes) continue;

    let edgeCount = 0;
    nodes.forEach((nodeId) => {
      const targets = adjacencyList.get(nodeId) || [];
      targets.forEach((targetId) => {
        if (nextLevelNodes.includes(targetId)) {
          edgeCount++;
        }
      });
    });

    maxEdgeCount = Math.max(maxEdgeCount, edgeCount);
  }

  // Increase spacing based on edge density
  const spacingMultiplier = Math.max(1, Math.sqrt(maxEdgeCount / 3));
  return Math.round(baseSpacing * spacingMultiplier);
};

/**
 * Node dimensions and bounding box utilities
 */
interface NodeBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Default node dimensions based on CustomNode component
const DEFAULT_NODE_WIDTH = 240; // min-w-[240px] from CustomNode
const DEFAULT_NODE_HEIGHT = 80; // Estimated height with padding

/**
 * Get bounding box for a node with padding
 */
const getNodeBounds = (
  nodeId: string,
  positions: Map<string, { x: number; y: number }>,
  padding: number = 0,
): NodeBounds => {
  const position = positions.get(nodeId);
  if (!position) {
    return {
      x: 0,
      y: 0,
      width: DEFAULT_NODE_WIDTH,
      height: DEFAULT_NODE_HEIGHT,
    };
  }

  return {
    x: position.x - padding,
    y: position.y - padding,
    width: DEFAULT_NODE_WIDTH + padding * 2,
    height: DEFAULT_NODE_HEIGHT + padding * 2,
  };
};

/**
 * Detect collisions between nodes
 */
const detectNodeNodeCollisions = (
  nodes: Node[],
  positions: Map<string, { x: number; y: number }>,
  minSpacing: number,
): Array<{
  nodeId1: string;
  nodeId2: string;
  adjustmentVector1: { x: number; y: number };
  adjustmentVector2: { x: number; y: number };
}> => {
  const collisions: Array<{
    nodeId1: string;
    nodeId2: string;
    adjustmentVector1: { x: number; y: number };
    adjustmentVector2: { x: number; y: number };
  }> = [];

  // Check each pair of nodes for overlap
  for (let i = 0; i < nodes.length; i++) {
    for (let j = i + 1; j < nodes.length; j++) {
      const node1 = nodes[i];
      const node2 = nodes[j];

      const pos1 = positions.get(node1.id);
      const pos2 = positions.get(node2.id);

      if (!pos1 || !pos2) continue;

      const bounds1 = getNodeBounds(node1.id, positions, 0);
      const bounds2 = getNodeBounds(node2.id, positions, 0);

      // Check if nodes overlap or are too close
      const overlapX = Math.max(
        0,
        Math.min(bounds1.x + bounds1.width, bounds2.x + bounds2.width) -
          Math.max(bounds1.x, bounds2.x),
      );
      const overlapY = Math.max(
        0,
        Math.min(bounds1.y + bounds1.height, bounds2.y + bounds2.height) -
          Math.max(bounds1.y, bounds2.y),
      );

      const centerDistance = Math.sqrt(
        Math.pow(pos1.x - pos2.x, 2) + Math.pow(pos1.y - pos2.y, 2),
      );

      const requiredDistance = Math.sqrt(
        Math.pow(DEFAULT_NODE_WIDTH + minSpacing, 2) +
          Math.pow(DEFAULT_NODE_HEIGHT + minSpacing, 2),
      );

      if ((overlapX > 0 && overlapY > 0) || centerDistance < requiredDistance) {
        // Calculate separation vector
        const dx = pos2.x - pos1.x;
        const dy = pos2.y - pos1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
          const moveDistance = (requiredDistance - distance) / 2 + 10; // Extra buffer
          const unitX = dx / distance;
          const unitY = dy / distance;

          collisions.push({
            nodeId1: node1.id,
            nodeId2: node2.id,
            adjustmentVector1: {
              x: -unitX * moveDistance,
              y: -unitY * moveDistance,
            },
            adjustmentVector2: {
              x: unitX * moveDistance,
              y: unitY * moveDistance,
            },
          });
        }
      }
    }
  }

  return collisions;
};

/**
 * Detect collisions between edges
 */
const detectEdgeEdgeCollisions = (
  edges: Edge[],
  positions: Map<string, { x: number; y: number }>,
  orientation: "horizontal" | "vertical",
  minSpacing: number = 20,
): Array<{
  edgeId1: string;
  edgeId2: string;
  adjustmentNeeded: boolean;
}> => {
  const collisions: Array<{
    edgeId1: string;
    edgeId2: string;
    adjustmentNeeded: boolean;
  }> = [];

  // Check each pair of edges for overlap
  for (let i = 0; i < edges.length; i++) {
    for (let j = i + 1; j < edges.length; j++) {
      const edge1 = edges[i];
      const edge2 = edges[j];

      // Skip if edges share source or target (allowed to overlap)
      if (
        edge1.source === edge2.source ||
        edge1.target === edge2.target ||
        edge1.source === edge2.target ||
        edge1.target === edge2.source
      ) {
        continue;
      }

      const source1Pos = positions.get(edge1.source);
      const target1Pos = positions.get(edge1.target);
      const source2Pos = positions.get(edge2.source);
      const target2Pos = positions.get(edge2.target);

      if (!source1Pos || !target1Pos || !source2Pos || !target2Pos) continue;

      // Get edge path segments
      const segments1 = getEdgePathSegments(
        source1Pos.x + DEFAULT_NODE_WIDTH / 2,
        source1Pos.y + DEFAULT_NODE_HEIGHT / 2,
        target1Pos.x + DEFAULT_NODE_WIDTH / 2,
        target1Pos.y + DEFAULT_NODE_HEIGHT / 2,
        orientation,
      );

      const segments2 = getEdgePathSegments(
        source2Pos.x + DEFAULT_NODE_WIDTH / 2,
        source2Pos.y + DEFAULT_NODE_HEIGHT / 2,
        target2Pos.x + DEFAULT_NODE_WIDTH / 2,
        target2Pos.y + DEFAULT_NODE_HEIGHT / 2,
        orientation,
      );

      // Check if any segments intersect or are too close
      let hasCollision = false;
      for (const seg1 of segments1) {
        for (const seg2 of segments2) {
          if (
            linesIntersect(seg1, seg2) ||
            getDistanceBetweenLines(seg1, seg2) < minSpacing
          ) {
            hasCollision = true;
            break;
          }
        }
        if (hasCollision) break;
      }

      if (hasCollision) {
        collisions.push({
          edgeId1: edge1.id,
          edgeId2: edge2.id,
          adjustmentNeeded: true,
        });
      }
    }
  }

  return collisions;
};

/**
 * Calculate distance between two line segments
 */
const getDistanceBetweenLines = (
  line1: { x1: number; y1: number; x2: number; y2: number },
  line2: { x1: number; y1: number; x2: number; y2: number },
): number => {
  // Simplified distance calculation - minimum distance between any two points
  const distances = [
    Math.sqrt(
      Math.pow(line1.x1 - line2.x1, 2) + Math.pow(line1.y1 - line2.y1, 2),
    ),
    Math.sqrt(
      Math.pow(line1.x1 - line2.x2, 2) + Math.pow(line1.y1 - line2.y2, 2),
    ),
    Math.sqrt(
      Math.pow(line1.x2 - line2.x1, 2) + Math.pow(line1.y2 - line2.y1, 2),
    ),
    Math.sqrt(
      Math.pow(line1.x2 - line2.x2, 2) + Math.pow(line1.y2 - line2.y2, 2),
    ),
  ];

  return Math.min(...distances);
};

/**
 * Calculate edge path segments for collision detection
 */
const getEdgePathSegments = (
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  orientation: "horizontal" | "vertical",
): Array<{ x1: number; y1: number; x2: number; y2: number }> => {
  const isHorizontal = orientation === "horizontal";
  const offset = isHorizontal ? 40 : 30; // Increased offset for better spacing

  // Get the smooth step path
  const [edgePath] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition: isHorizontal ? Position.Right : Position.Bottom,
    targetX,
    targetY,
    targetPosition: isHorizontal ? Position.Left : Position.Top,
    borderRadius: 8,
    offset,
  });

  // Parse SVG path to get segments (simplified approach)
  // For collision detection, we'll use key points of the smooth step path
  const segments: Array<{ x1: number; y1: number; x2: number; y2: number }> =
    [];

  if (isHorizontal) {
    // Horizontal layout: source -> right -> down/up -> left -> target
    const midX = sourceX + offset;
    const midX2 = targetX - offset;

    segments.push(
      { x1: sourceX, y1: sourceY, x2: midX, y2: sourceY }, // Horizontal from source
      { x1: midX, y1: sourceY, x2: midX, y2: targetY }, // Vertical connector
      { x1: midX, y1: targetY, x2: midX2, y2: targetY }, // Horizontal middle
      { x1: midX2, y1: targetY, x2: targetX, y2: targetY }, // Horizontal to target
    );
  } else {
    // Vertical layout: source -> down -> right/left -> up -> target
    const midY = sourceY + offset;
    const midY2 = targetY - offset;

    segments.push(
      { x1: sourceX, y1: sourceY, x2: sourceX, y2: midY }, // Vertical from source
      { x1: sourceX, y1: midY, x2: targetX, y2: midY }, // Horizontal connector
      { x1: targetX, y1: midY, x2: targetX, y2: midY2 }, // Vertical middle
      { x1: targetX, y1: midY2, x2: targetX, y2: targetY }, // Vertical to target
    );
  }

  return segments;
};

/**
 * Check if a line segment intersects with a rectangle
 */
const lineIntersectsRect = (
  line: { x1: number; y1: number; x2: number; y2: number },
  rect: NodeBounds,
): boolean => {
  const { x1, y1, x2, y2 } = line;
  const { x, y, width, height } = rect;

  // Check if line endpoints are inside rectangle
  const p1Inside = x1 >= x && x1 <= x + width && y1 >= y && y1 <= y + height;
  const p2Inside = x2 >= x && x2 <= x + width && y2 >= y && y2 <= y + height;

  if (p1Inside || p2Inside) return true;

  // Check line intersection with rectangle edges
  const rectLines = [
    { x1: x, y1: y, x2: x + width, y2: y }, // Top edge
    { x1: x + width, y1: y, x2: x + width, y2: y + height }, // Right edge
    { x1: x + width, y1: y + height, x2: x, y2: y + height }, // Bottom edge
    { x1: x, y1: y + height, x2: x, y2: y }, // Left edge
  ];

  return rectLines.some((rectLine) => linesIntersect(line, rectLine));
};

/**
 * Check if two line segments intersect
 */
const linesIntersect = (
  line1: { x1: number; y1: number; x2: number; y2: number },
  line2: { x1: number; y1: number; x2: number; y2: number },
): boolean => {
  const { x1: x1a, y1: y1a, x2: x2a, y2: y2a } = line1;
  const { x1: x1b, y1: y1b, x2: x2b, y2: y2b } = line2;

  const denom = (y2b - y1b) * (x2a - x1a) - (x2b - x1b) * (y2a - y1a);
  if (denom === 0) return false; // Lines are parallel

  const ua = ((x2b - x1b) * (y1a - y1b) - (y2b - y1b) * (x1a - x1b)) / denom;
  const ub = ((x2a - x1a) * (y1a - y1b) - (y2a - y1a) * (x1a - x1b)) / denom;

  return ua >= 0 && ua <= 1 && ub >= 0 && ub <= 1;
};

/**
 * Detect collisions between edges and nodes
 */
const detectEdgeNodeCollisions = (
  nodes: Node[],
  edges: Edge[],
  positions: Map<string, { x: number; y: number }>,
  orientation: "horizontal" | "vertical",
  minSpacing: number,
): Array<{
  nodeId: string;
  edgeId: string;
  adjustmentVector: { x: number; y: number };
}> => {
  const collisions: Array<{
    nodeId: string;
    edgeId: string;
    adjustmentVector: { x: number; y: number };
  }> = [];

  // Check each edge against each node (except source and target)
  edges.forEach((edge) => {
    const sourcePos = positions.get(edge.source);
    const targetPos = positions.get(edge.target);

    if (!sourcePos || !targetPos) return;

    // Calculate edge center point for handle positions
    const sourceCenterX = sourcePos.x + DEFAULT_NODE_WIDTH / 2;
    const sourceCenterY = sourcePos.y + DEFAULT_NODE_HEIGHT / 2;
    const targetCenterX = targetPos.x + DEFAULT_NODE_WIDTH / 2;
    const targetCenterY = targetPos.y + DEFAULT_NODE_HEIGHT / 2;

    // Get edge path segments
    const segments = getEdgePathSegments(
      sourceCenterX,
      sourceCenterY,
      targetCenterX,
      targetCenterY,
      orientation,
    );

    // Check collision with each node (except source and target)
    nodes.forEach((node) => {
      if (node.id === edge.source || node.id === edge.target) return;

      const nodeBounds = getNodeBounds(node.id, positions, minSpacing);

      // Check if any edge segment intersects with the node bounds
      const hasCollision = segments.some((segment) =>
        lineIntersectsRect(segment, nodeBounds),
      );

      if (hasCollision) {
        // Calculate adjustment vector to move node away from edge
        const nodeCenter = {
          x: nodeBounds.x + nodeBounds.width / 2,
          y: nodeBounds.y + nodeBounds.height / 2,
        };

        // Find closest point on edge to node center
        let minDistance = Infinity;
        let adjustmentVector = { x: 0, y: 0 };

        segments.forEach((segment) => {
          const closestPoint = getClosestPointOnLine(nodeCenter, segment);
          const distance = Math.sqrt(
            Math.pow(nodeCenter.x - closestPoint.x, 2) +
              Math.pow(nodeCenter.y - closestPoint.y, 2),
          );

          if (distance < minDistance) {
            minDistance = distance;
            const requiredDistance = minSpacing + 20; // Extra buffer
            const currentDistance = distance;
            const moveDistance = requiredDistance - currentDistance;

            if (moveDistance > 0) {
              const directionX = nodeCenter.x - closestPoint.x;
              const directionY = nodeCenter.y - closestPoint.y;
              const length = Math.sqrt(
                directionX * directionX + directionY * directionY,
              );

              if (length > 0) {
                adjustmentVector = {
                  x: (directionX / length) * moveDistance,
                  y: (directionY / length) * moveDistance,
                };
              }
            }
          }
        });

        if (adjustmentVector.x !== 0 || adjustmentVector.y !== 0) {
          collisions.push({
            nodeId: node.id,
            edgeId: edge.id,
            adjustmentVector,
          });
        }
      }
    });
  });

  return collisions;
};

/**
 * Get closest point on a line segment to a given point
 */
const getClosestPointOnLine = (
  point: { x: number; y: number },
  line: { x1: number; y1: number; x2: number; y2: number },
): { x: number; y: number } => {
  const { x, y } = point;
  const { x1, y1, x2, y2 } = line;

  const dx = x2 - x1;
  const dy = y2 - y1;
  const length = dx * dx + dy * dy;

  if (length === 0) return { x: x1, y: y1 };

  const t = Math.max(0, Math.min(1, ((x - x1) * dx + (y - y1) * dy) / length));

  return {
    x: x1 + t * dx,
    y: y1 + t * dy,
  };
};

/**
 * Calculates auto-layout positions for nodes with improved spacing and alignment
 */
export const calculateAutoLayout = (
  nodes: Node[],
  edges: Edge[],
  options: LayoutOptions = {},
): AutoLayoutResult => {
  const {
    horizontalSpacing = 400, // Increased default spacing
    verticalSpacing = 250, // Increased default spacing
    startX = 100,
    startY = 100,
    alignByLayer = true,
    handleOrientation = "auto",
    minimizeEdgeCrossings = true,
    edgeCrossingIterations = 3,
    dynamicSpacing = true,
    edgeNodeSpacing = 80, // Increased default spacing
    edgeNodeCollisionIterations = 5, // Increased iterations
  } = options;

  if (nodes.length === 0) {
    return { positions: new Map(), orientation: "horizontal" };
  }

  const orientation =
    handleOrientation === "auto"
      ? detectHandleOrientation(nodes)
      : handleOrientation;

  // Create adjacency list and calculate in-degrees
  const adjacencyList = new Map<string, string[]>();
  const reverseAdjacencyList = new Map<string, string[]>();
  const inDegree = new Map<string, number>();

  // Initialize
  nodes.forEach((node) => {
    adjacencyList.set(node.id, []);
    reverseAdjacencyList.set(node.id, []);
    inDegree.set(node.id, 0);
  });

  // Build graph
  edges.forEach((edge) => {
    if (edge.source && edge.target) {
      adjacencyList.get(edge.source)?.push(edge.target);
      reverseAdjacencyList.get(edge.target)?.push(edge.source);
      inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
    }
  });

  // Topological sort to determine levels
  const levels = new Map<string, number>();
  const levelNodes = new Map<number, string[]>();
  const queue: string[] = [];

  // Find root nodes (nodes with no incoming edges)
  nodes.forEach((node) => {
    if (inDegree.get(node.id) === 0) {
      queue.push(node.id);
      levels.set(node.id, 0);
    }
  });

  // If no root nodes, use the first node as root
  if (queue.length === 0 && nodes.length > 0) {
    const firstNode = nodes[0];
    queue.push(firstNode.id);
    levels.set(firstNode.id, 0);
  }

  // BFS to assign levels
  while (queue.length > 0) {
    const nodeId = queue.shift()!;
    const currentLevel = levels.get(nodeId) || 0;

    // Add to level tracking
    if (!levelNodes.has(currentLevel)) {
      levelNodes.set(currentLevel, []);
    }
    levelNodes.get(currentLevel)!.push(nodeId);

    // Process children
    const children = adjacencyList.get(nodeId) || [];
    children.forEach((childId) => {
      const childLevel = Math.max(levels.get(childId) || 0, currentLevel + 1);
      levels.set(childId, childLevel);

      // Decrease in-degree and add to queue if ready
      const newInDegree = (inDegree.get(childId) || 0) - 1;
      inDegree.set(childId, newInDegree);

      if (newInDegree === 0) {
        queue.push(childId);
      }
    });
  }

  // Handle any remaining nodes (cycles or disconnected components)
  nodes.forEach((node) => {
    if (!levels.has(node.id)) {
      const maxLevel = Math.max(...Array.from(levels.values()), -1);
      levels.set(node.id, maxLevel + 1);

      const level = maxLevel + 1;
      if (!levelNodes.has(level)) {
        levelNodes.set(level, []);
      }
      levelNodes.get(level)!.push(node.id);
    }
  });

  // Apply edge crossing minimization if enabled
  let optimizedLevelNodes = levelNodes;
  if (minimizeEdgeCrossings && edges.length > 0) {
    optimizedLevelNodes = minimizeEdgeCrossingsWithBarycenter(
      levelNodes,
      adjacencyList,
      reverseAdjacencyList,
      orientation,
      edgeCrossingIterations,
    );
  }

  // Calculate dynamic spacing if enabled
  const finalHorizontalSpacing = dynamicSpacing
    ? calculateDynamicSpacing(
        optimizedLevelNodes,
        adjacencyList,
        horizontalSpacing,
        orientation,
      )
    : horizontalSpacing;
  const finalVerticalSpacing = dynamicSpacing
    ? calculateDynamicSpacing(
        optimizedLevelNodes,
        adjacencyList,
        verticalSpacing,
        orientation,
      )
    : verticalSpacing;

  // Calculate positions based on orientation
  const positions = new Map<string, { x: number; y: number }>();

  if (orientation === "horizontal") {
    // Horizontal layout (left to right)
    optimizedLevelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length;
      const levelHeight = (totalNodesInLevel - 1) * finalVerticalSpacing;
      const startYForLevel = startY - levelHeight / 2;

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startX + level * finalHorizontalSpacing,
          y: startYForLevel + indexInLevel * finalVerticalSpacing,
        });
      });
    });
  } else {
    // Vertical layout (top to bottom)
    optimizedLevelNodes.forEach((nodeIds, level) => {
      const totalNodesInLevel = nodeIds.length;
      const levelWidth = (totalNodesInLevel - 1) * finalHorizontalSpacing;
      const startXForLevel = startX - levelWidth / 2;

      nodeIds.forEach((nodeId, indexInLevel) => {
        positions.set(nodeId, {
          x: startXForLevel + indexInLevel * finalHorizontalSpacing,
          y: startY + level * finalVerticalSpacing,
        });
      });
    });
  }

  // Apply comprehensive collision detection and resolution
  if (edgeNodeSpacing > 0) {
    let adjustedPositions = new Map(positions);
    const minNodeSpacing = Math.min(horizontalSpacing, verticalSpacing) * 0.3;

    for (
      let iteration = 0;
      iteration < edgeNodeCollisionIterations;
      iteration++
    ) {
      let hasAnyCollisions = false;
      const allAdjustments = new Map<string, { x: number; y: number }>();

      // 1. Detect and resolve node-to-node collisions
      const nodeCollisions = detectNodeNodeCollisions(
        nodes,
        adjustedPositions,
        minNodeSpacing,
      );

      if (nodeCollisions.length > 0) {
        hasAnyCollisions = true;
        nodeCollisions.forEach(
          ({ nodeId1, nodeId2, adjustmentVector1, adjustmentVector2 }) => {
            // Accumulate adjustments for both nodes
            const adj1 = allAdjustments.get(nodeId1) || { x: 0, y: 0 };
            const adj2 = allAdjustments.get(nodeId2) || { x: 0, y: 0 };

            allAdjustments.set(nodeId1, {
              x: adj1.x + adjustmentVector1.x,
              y: adj1.y + adjustmentVector1.y,
            });

            allAdjustments.set(nodeId2, {
              x: adj2.x + adjustmentVector2.x,
              y: adj2.y + adjustmentVector2.y,
            });
          },
        );
      }

      // 2. Detect edge-to-edge collisions and adjust spacing if needed
      if (edges.length > 1) {
        const edgeCollisions = detectEdgeEdgeCollisions(
          edges,
          adjustedPositions,
          orientation,
          20, // Minimum edge spacing
        );

        if (edgeCollisions.length > 0) {
          hasAnyCollisions = true;
          // For edge collisions, we increase spacing between affected nodes
          edgeCollisions.forEach(({ edgeId1, edgeId2 }) => {
            const edge1 = edges.find((e) => e.id === edgeId1);
            const edge2 = edges.find((e) => e.id === edgeId2);

            if (edge1 && edge2) {
              // Move nodes slightly to reduce edge overlap
              const sourcePos1 = adjustedPositions.get(edge1.source);
              const targetPos1 = adjustedPositions.get(edge1.target);
              const sourcePos2 = adjustedPositions.get(edge2.source);
              const targetPos2 = adjustedPositions.get(edge2.target);

              if (sourcePos1 && targetPos1 && sourcePos2 && targetPos2) {
                // Apply small adjustments to separate overlapping edges
                const separationVector =
                  orientation === "horizontal"
                    ? { x: 0, y: 30 }
                    : { x: 30, y: 0 };

                [edge1.source, edge1.target].forEach((nodeId) => {
                  const adj = allAdjustments.get(nodeId) || { x: 0, y: 0 };
                  allAdjustments.set(nodeId, {
                    x: adj.x + separationVector.x,
                    y: adj.y + separationVector.y,
                  });
                });

                [edge2.source, edge2.target].forEach((nodeId) => {
                  const adj = allAdjustments.get(nodeId) || { x: 0, y: 0 };
                  allAdjustments.set(nodeId, {
                    x: adj.x - separationVector.x,
                    y: adj.y - separationVector.y,
                  });
                });
              }
            }
          });
        }
      }

      // 3. Detect and resolve edge-to-node collisions
      if (edges.length > 0) {
        const edgeNodeCollisions = detectEdgeNodeCollisions(
          nodes,
          edges,
          adjustedPositions,
          orientation,
          edgeNodeSpacing,
        );

        if (edgeNodeCollisions.length > 0) {
          hasAnyCollisions = true;
          edgeNodeCollisions.forEach(({ nodeId, adjustmentVector }) => {
            const adj = allAdjustments.get(nodeId) || { x: 0, y: 0 };
            allAdjustments.set(nodeId, {
              x: adj.x + adjustmentVector.x,
              y: adj.y + adjustmentVector.y,
            });
          });
        }
      }

      // Apply all accumulated adjustments
      allAdjustments.forEach((adjustment, nodeId) => {
        const currentPos = adjustedPositions.get(nodeId);
        if (currentPos) {
          adjustedPositions.set(nodeId, {
            x: currentPos.x + adjustment.x * 0.5, // Damping factor to prevent oscillation
            y: currentPos.y + adjustment.y * 0.5,
          });
        }
      });

      // Break if no collisions detected
      if (!hasAnyCollisions) break;
    }

    return { positions: adjustedPositions, orientation };
  }

  return { positions, orientation };
};

/**
 * Easing function for smooth animations
 */
export const easeOutCubic = (t: number): number => 1 - (1 - t) ** 3;

/**
 * Enhanced auto-layout function with smooth animations
 */
export const applyAutoLayoutSmooth = (
  nodes: Node[],
  edges: Edge[],
  updateNodes: (nodes: Node[]) => void,
  fitView?: (options?: { padding?: number; duration?: number }) => void,
  options: LayoutOptions = {},
): void => {
  const { animationDuration = 500, onComplete, ...layoutOptions } = options;

  if (nodes.length === 0) return;

  const { positions: targetPositions, orientation } = calculateAutoLayout(
    nodes,
    edges,
    layoutOptions,
  );

  if (targetPositions.size === 0) return;

  // Store initial positions
  const initialPositions = new Map<string, { x: number; y: number }>();
  nodes.forEach((node) => {
    initialPositions.set(node.id, { x: node.position.x, y: node.position.y });
  });

  const startTime = Date.now();

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);
    const easedProgress = easeOutCubic(progress);

    // Update node positions
    const updatedNodes = nodes.map((node) => {
      const initialPos = initialPositions.get(node.id);
      const targetPos = targetPositions.get(node.id);

      if (!initialPos || !targetPos) return node;

      const newPosition = {
        x: initialPos.x + (targetPos.x - initialPos.x) * easedProgress,
        y: initialPos.y + (targetPos.y - initialPos.y) * easedProgress,
      };

      return {
        ...node,
        position: newPosition,
      };
    });

    updateNodes(updatedNodes);

    if (progress < 1) {
      requestAnimationFrame(animate);
    } else {
      // Animation complete
      if (fitView) {
        fitView({
          padding: 0.2,
          duration: 400,
        });
      }

      if (onComplete) {
        onComplete(targetPositions);
      }
    }
  };

  animate();
};

/**
 * Debounced auto layout to prevent rapid triggering
 */
export const createDebouncedAutoLayout = (
  autoLayoutFn: () => void,
  delay: number = 250,
) => {
  let timeoutId: NodeJS.Timeout | null = null;

  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      autoLayoutFn();
      timeoutId = null;
    }, delay);

    // Return cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };
  };
};
