import { calculateAutoLayout } from '../autoLayout'
import type { Node, Edge } from '@xyflow/react'

describe('AutoLayout Edge Overlap Prevention', () => {
  // Helper function to create test nodes
  const createNode = (id: string): Node => ({
    id,
    type: 'custom',
    position: { x: 0, y: 0 },
    data: { label: `Node ${id}` }
  })

  // Helper function to create test edges
  const createEdge = (source: string, target: string): Edge => ({
    id: `${source}-${target}`,
    source,
    target
  })

  test('should minimize edge crossings with barycenter heuristic', () => {
    // Create a test graph that would have edge crossings without optimization
    const nodes: Node[] = [
      createNode('A'),
      createNode('B'),
      createNode('C'),
      createNode('D'),
      createNode('E'),
      createNode('F')
    ]

    const edges: Edge[] = [
      createEdge('A', 'D'),
      createEdge('A', 'E'),
      createEdge('B', 'D'),
      createEdge('B', 'F'),
      createEdge('C', 'E'),
      createEdge('C', 'F')
    ]

    const result = calculateAutoLayout(nodes, edges, {
      minimizeEdgeCrossings: true,
      edgeCrossingIterations: 3,
      horizontalSpacing: 300,
      verticalSpacing: 200
    })

    expect(result.positions.size).toBe(6)
    expect(result.orientation).toBe('horizontal')

    // Verify that nodes are positioned
    const positionA = result.positions.get('A')
    const positionD = result.positions.get('D')
    
    expect(positionA).toBeDefined()
    expect(positionD).toBeDefined()
    
    if (positionA && positionD) {
      // In horizontal layout, target nodes should be to the right of source nodes
      expect(positionD.x).toBeGreaterThan(positionA.x)
    }
  })

  test('should apply dynamic spacing based on edge density', () => {
    // Create a graph with high edge density
    const nodes: Node[] = [
      createNode('A'),
      createNode('B'),
      createNode('C'),
      createNode('D'),
      createNode('E'),
      createNode('F')
    ]

    const edges: Edge[] = [
      createEdge('A', 'D'),
      createEdge('A', 'E'),
      createEdge('A', 'F'),
      createEdge('B', 'D'),
      createEdge('B', 'E'),
      createEdge('B', 'F'),
      createEdge('C', 'D'),
      createEdge('C', 'E'),
      createEdge('C', 'F')
    ]

    const resultWithDynamicSpacing = calculateAutoLayout(nodes, edges, {
      dynamicSpacing: true,
      horizontalSpacing: 300,
      verticalSpacing: 200
    })

    const resultWithoutDynamicSpacing = calculateAutoLayout(nodes, edges, {
      dynamicSpacing: false,
      horizontalSpacing: 300,
      verticalSpacing: 200
    })

    expect(resultWithDynamicSpacing.positions.size).toBe(6)
    expect(resultWithoutDynamicSpacing.positions.size).toBe(6)

    // Both should position all nodes
    const positionA1 = resultWithDynamicSpacing.positions.get('A')
    const positionA2 = resultWithoutDynamicSpacing.positions.get('A')
    
    expect(positionA1).toBeDefined()
    expect(positionA2).toBeDefined()
  })

  test('should handle empty graph gracefully', () => {
    const result = calculateAutoLayout([], [], {
      minimizeEdgeCrossings: true,
      dynamicSpacing: true
    })

    expect(result.positions.size).toBe(0)
    expect(result.orientation).toBe('horizontal')
  })

  test('should handle single node gracefully', () => {
    const nodes: Node[] = [createNode('A')]
    const edges: Edge[] = []

    const result = calculateAutoLayout(nodes, edges, {
      minimizeEdgeCrossings: true,
      dynamicSpacing: true
    })

    expect(result.positions.size).toBe(1)
    expect(result.positions.get('A')).toBeDefined()
  })

  test('should handle disconnected components', () => {
    const nodes: Node[] = [
      createNode('A'),
      createNode('B'),
      createNode('C'),
      createNode('D')
    ]

    const edges: Edge[] = [
      createEdge('A', 'B'),
      createEdge('C', 'D')
    ]

    const result = calculateAutoLayout(nodes, edges, {
      minimizeEdgeCrossings: true,
      dynamicSpacing: true
    })

    expect(result.positions.size).toBe(4)
    
    // All nodes should be positioned
    nodes.forEach(node => {
      expect(result.positions.get(node.id)).toBeDefined()
    })
  })

  test('should respect orientation setting', () => {
    const nodes: Node[] = [
      createNode('A'),
      createNode('B')
    ]

    const edges: Edge[] = [
      createEdge('A', 'B')
    ]

    const horizontalResult = calculateAutoLayout(nodes, edges, {
      handleOrientation: 'horizontal'
    })

    const verticalResult = calculateAutoLayout(nodes, edges, {
      handleOrientation: 'vertical'
    })

    expect(horizontalResult.orientation).toBe('horizontal')
    expect(verticalResult.orientation).toBe('vertical')
  })
})
